{"cells": [{"cell_type": "code", "execution_count": 4, "id": "3c5b7de0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted text from intensification-paper.pdf\n"]}], "source": ["import PyPDF2\n", "import os\n", "\n", "# list files of current directory\n", "files = os.listdir()\n", "pdf_files = [f for f in files if f.endswith(\".pdf\")]\n", "\n", "for pdf_file in pdf_files:\n", "    with open(pdf_file, \"rb\") as file:\n", "        reader = PyPDF2.PdfReader(file)\n", "        text = \"\"\n", "        for page in reader.pages:\n", "            text += page.extract_text() + \"\\n\"\n", "        with open(pdf_file.replace(\".pdf\", \".txt\"), \"w\") as text_file:\n", "            text_file.write(text)\n", "        print(f\"Extracted text from {pdf_file}\")"]}, {"cell_type": "code", "execution_count": null, "id": "3b495c28", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Routine immunization\\nintensiﬁcation, vaccination\\ncampaigns, and measles\\ntransmission in Southern Nigeria\\nNiket Thakkar1,*, <PERSON><PERSON><PERSON>, <PERSON>\\n*For correspondence:\\<EMAIL> ;\\nhttps://nthakkar.github.io/\\nCode repository:\\nhttps://github.com/\\nNThakkar-IDM/intensification1The Institute for Disease Modeling, Bill &Melinda Gates Foundation, Seattle\\nWashington 98109 ;2Nigeria Country Oﬃce, Bill &Melinda Gates Foundation, Abuja\\n904101, Federal Capital Territory, Nigeria\\nAbstract In this paper, we compare – in terms of their estimated effects on disease\\ntransmission – Southern Nigeria’s large-scale measles vaccination campaigns since 2010 to a\\nunique, more targeted routine immunization intensiﬁcation that happened in 2019. A main focus\\nof the discussion throughout is that quantifying intervention impact in real epidemiologies\\nrequires us to disentangle competing, dynamic sources of immunity, including unreported\\ninfection. To address this inference challenge, we create a collection of state-level, stochastic\\ntransmission models capable of estimating underlying measles susceptibility based on\\nsurveillance and survey data. Leveraging these models, we ﬁnd that the 2019 intensiﬁcation,\\ndespite being restricted in scale to children under 2 years-old, had an effect on transmission\\ncomparable to the region’s larger vaccination campaigns targeting children up to 5. This implies\\nthat vaccines delivered in that effort were more than twice as likely to reach a susceptible child.\\nIntroduction\\nAccess to measles vaccine is a fundamental global equity issue. While much of the world gets vac-\\ncinated as a routine health service, health systems in many countries are unstable and unreliable\\n(Measles & Rubella Partnership, 2020). Millions of children are lost to routine systems every year,\\nand with a lack of mechanisms for effective catch-up vaccination, they cannot get vaccine on time\\nand are chronically left at risk.\\nMeasles burden is also getting worse. There were an estimated 130000 measles deaths in 2022\\n(Minta et al., 2023), and cases reported to the WHO have increased since. The virus is highly conta-\\ngious and particularly devastating for children under 5, with the risk of complications compounded\\nby systemic issues like malnutrition or poor access to treatment ( Wolfson et al., 2009). Essentially\\nall of this burden, which disproportionately affects the world’s most vulnerable communities, is vac-\\ncine preventable (Measles & Rubella Partnership, 2020 ), but global measles vaccination coverage\\nhas been essentially stagnant for nearly 15 years (World Health Organization, 2024).\\nGiven this context, improving catch-up vaccination through routine systems and through con-\\ncerted vaccination campaigns is a critical global health challenge. Making progress on it requires\\nus to better understand what’s worked well in the past and to translate those lessons into more\\neffective implementation in the future. Research along these lines is evolving – studies have demon-\\nstrated the signiﬁcance of campaign timing (Rosenfeld et al., 2024 ;Thakkar et al., 2019 ), outbreak\\n1 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nNOTE: This preprint reports new research that has not been certified by peer review and should not be used to guide clinical practice.\\nresponse speed (Grais et al., 2008), and microplan quality ( Oteri et al., 2021). But many more\\nfactors determine if a vaccine reaches a child before measles does.\\nThis paper contributes to this broader effort. Speciﬁcally, we consider catch-up vaccination in\\nSouthern Nigeria, where immunity gaps have generally been addressed with large-scale vaccina-\\ntion campaigns targeting all children from 9 months to 5 years old. Five of these efforts have been\\nundertaken since 2010, an example of a real-world implementation of the recommended catch-up\\nfrequency (World Health Organization, 2016; Verguet et al., 2015 ), but in 2019, Nigerian health au-\\nthorities implemented an intensiﬁcation of routine immunization (IRI) as part of the introduction\\nof a second measles dose (MCV2) into routine services. That unique effort targeted 9 month to 2\\nyear olds only and, unlike the campaigns, offered the full set of routinely delivered vaccines.\\nComparing Southern Nigeria’s 2019 IRI to its campaigns gives us an opportunity to understand\\nhow changes in age targeting and cadence affect outcomes in a high-burden setting. However,\\nestimating intervention impact in real epidemiologies can be challenging. While there are direct\\nmeasures of implementation quality, most notably post-campaign coverage surveys that measure\\nhow comprehensively doses were distributed, comparing interventions to one-another requires\\nus to place them into context with disease-derived immunity, changes in routine immunization\\ncoverage, demographic shifts, and other potentially confounding dynamics.\\nWe approach this inference challenge by creating detailed, state-level stochastic-process mod-\\nels of measles transmission for all 17 Southern states. These models take as input a broad dataset,\\nincluding case-based reporting, household survey, and catch-up implementation data, and then\\nestimate consistent epidemiological quantities like population susceptibility, transmission season-\\nality, and the rate of under-reporting. Taken together, across state-level models, we ﬁnd that the\\nvaccines delivered in the 2019 IRI were more than twice as likely to immunize a susceptible child\\nas the vaccines delivered in the campaigns since 2010.\\nThis paper is organized as follows. In the ﬁrst section, we describe the input data and transmis-\\nsion model construction for Lagos State, which serves as a representative example. We show that\\nthe model is consistent with independent serological survey results and is predictive of future cases\\non a 3 year forecast horizon. Then, in the next section, we use the Lagos model to estimate the frac-\\ntion of catch-up doses that immunized a susceptible child by event, which highlights the 2019 IRI as\\nexceptional. Aggregating models across the South demonstrates that this result is robust. Finally,\\nwe conclude by discussing qualitative lessons for catch-up vaccine delivery, commenting on how\\nthis example contrasts with current guidance and offers empirical support for higher-frequency,\\nnarrower age-range efforts going forward.\\nModeling measles transmission – Lagos case study\\nEpidemiological data\\nThe data for Lagos State is broken into categories and visualized in Fig. 1. As mentioned, inputs\\nspan multiple sources, but the primary sources are Nigeria’s case-based surveillance system from\\n2009 through 2023, and household surveys, speciﬁcally the Demographics and Health Surveys\\n(DHSs) and the Multiple Indicator Cluster Surveys (MICSs), from 2008 to 2021 (NPC and ICF, 2009 ,\\n2014, 2019; NBS and UNICEF, 2012 ,2017, 2022).\\nInformation from the case-based surveillance system is shown in red (top row). Measles cases\\nthat are lab conﬁrmed, epidemiologically linked, or have a high estimated probability of conﬁrma-\\ntion based on individual age, self-reported vaccine history, and symptom-onset time ( Appendix 1)\\nare aggregated daily. Lagos State’s incidence curve suggests a period of increased control after the\\n2013 vaccination campaign, and then a resurgence starting in 2021, in parallel with much of the\\nworld after Covid-19 disruptions to immunization programs ( Gaythorpe et al., 2021). Aggregated\\nover time, the age distribution of cases (red steps) peaks for 1 to 2 year-olds, with a steady expo-\\nnential decline for older ages, characteristic of a high-transmission-rate setting with initial maternal\\nprotection ( Anderson and May, 1992 ).\\n2 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nFigure 1. Epidemiological data for Lagos State. (Top) Both the time series of measles cases (red bars) and\\nassociated age-at-symptom onset data (red steps) come from Nigeria’s case-based surveillance system.\\n(Middle) We estimate the number of children born every month (blue, with 95 %interval shaded) based on\\nhousehold survey data from 2008 to 2021. (Bottom) MCV1 coverage (purple, 95% interval shaded) is also\\nbased on the surveys, while catch-up event size (yellow bars) is estimated based on data from the WHO.\\nIn Fig. 1’s middle panel, Lagos State’s monthly births are estimated based on survey data. Specif-\\nically, we use a regression with post-stratiﬁcation approach to estimate yearly births, and then we\\nallocate them to months using an empirical seasonality proﬁle based on surveyed birth dates. The\\naverage yearly birthrate associated with Fig. 1is roughly 28 births per 1000 population, within the\\nPoisson interval of other estimates for urban Nigeria (NPC and ICF, 2019 ). Sub-annually however,\\nbirth seasonality is a prominent effect across states, and in Lagos, we ﬁnd that ﬂuctuations reach\\nnearly 30 %, an estimate well within the range of country-level ﬁgures across Sub-Saharan Africa\\n(Dorélien, 2016 ).\\nFinally, in Fig. 1’s bottom panel, we visualize the components of vaccine delivery. Coverage\\namongst 12 to 23 month-olds (purple), estimated with a similar regression and post stratiﬁcation\\napproach, is indicative of ﬁrst dose routine coverage (MCV1) and shows steady increases from\\n2008 to 2024. Oﬃcial estimates from individual surveys (black dots) give some modest validation\\nof the statistical interpolation. Meanwhile, vaccination campaigns are indicated in yellow, with lines\\nproportional to the number of doses delivered according to the WHO ( World Health Organization,\\n2024). MCV2 introduction and the accompanying IRI are also indicated, with signiﬁcantly fewer\\nMCV doses delivered in that more targeted catch-up effort (Appendix 1).\\nAs a whole, Fig. 1highlights issues that motivate the need for modeling. For example, we esti-\\nmate that roughly 30 thousand kids are born in Lagos State every month, and around 6 thousand of\\nthem are missed by routine MCV1. For the 54thousand missed children born in the 9 months pre-\\n3 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nFigure 2. Measles transmission in Lagos State. (Top) The model (grey, 95 %interval shaded) captures changes\\nin cases (black dots) including the transitions to and from control, seen more clearly in terms of underlying\\nprevalence (orange). (Middle) Model-based estimates of susceptibility (blue) rise as children are missed by\\nroutine immunization and fall during catch-up events, with an overall level corroborated by the serological\\nsurvey in 2018 (black bar). (Bottom) The implied probability that infections are reported as cases (green) is\\nlow, dynamic, and volatile.\\nceding a vaccination campaign, the age distribution of cases suggests that they experience nearly\\nhalf of their infection risk before their next catch-up opportunity. Even in an extreme hypothetical\\nwhere lifetime risk is determined by a 1%vaccine failure rate (Halsey et al., 1985), we would still\\nexpect a few hundred cases per year from just one of these cohorts. But since 2009, Lagos State\\nhas reported less than 1200 cases in total, around 75 per year. Reconciling these apparent discrep-\\nancies, as well as estimating the impact of catch-ups on immunity, requires us to put the pieces\\ntogether carefully.\\nModel construction\\nTransmission models offer a guide to resolving the measurements in Fig. 1into a consistent epi-\\ndemiological process. For our purposes here, we follow the approach in Thakkar et al. (2024)\\nclosely – mathematical details can be found in Appendix 2 – and we assume that population can be\\ncompartmentalized into susceptible, infectious, and recovered states on a semi-monthly timescale\\n(Finkenstädt and Grenfell, 2000; Ferrari et al., 2008 ). Births, averaged to the same timescale, en-\\nter the susceptible population but are removed in proportion to routine MCV1 and MCV2 cover-\\nage after 9 and 15 months respectively. Those remaining are otherwise at risk of getting measles\\nthrough interactions with infectious individuals, and new infections transition to being infectious\\nwithin a semi-month before being removed from the system as well. Finally, during the six catch-\\nup vaccination events from 2009 onward, susceptible individuals have additional opportunities to\\nbe immunized.\\nThe model is ﬁt to the incidence time series in Fig. 1conditional on the age, demographic, and\\nvaccination-related information. We assume that the fraction of susceptible-infectious pairs lead-\\n4 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\ning to new infections is log-normally distributed with a seasonally varying mean ( Finkenstädt and\\nGrenfell, 2000 ) which we correlate across the states in Nigeria’s geopolitical zones (collections of\\n5 or 6 contiguous states). Further, we assume infections are under-reported at an unknown, dy-\\nnamic rate bounded by the expected annual burden consistent with the age distribution of cases\\nin a survival analysis estimating the proportion of birth-cohorts left exposed across vaccination op-\\nportunities. Catch-up vaccination events are deﬁned in the model by an eﬃcacy parameter which\\nrepresents the fraction of delivered vaccines that successfully immunized susceptible individuals.\\nThe application of these ideas to Lagos State is visualized in Fig. 2. In the top panel, the model\\n(grey, 95% interval shaded) ﬁts reported cases (black dots) closely with an underlying population\\nprevalence (orange) reaching 1 per 1000 during outbreaks, similar to ﬁgures in other settings\\n(Finkenstädt and Grenfell, 2000). The transmission process is driven by underlying susceptibility\\n(blue), which grows steadily as children are missed by routine immunization but falls precipitously\\nat each of the 6 catch-up events (yellow bars). The overall level of susceptibility, roughly 6%of\\nthe population, is in very good agreement with the level measured by serological survey in 2018\\n(Nakase et al. (2024), grey bar), but with considerably higher conﬁdence. Moreover, as we expected\\nbased on Fig. 1, catch-up vaccination efforts from 2014 to 2020 closed large immunity gaps and\\ncontrolled burden until late 2021.\\nIn relating population prevalence to cases, we estimate that 0.24 %(0.09 to 0.4% 95% conﬁdence\\ninterval) of infections are reported to the surveillance system on average, with volatility in time\\n(green) clearly proportional to, and maybe even a leading indicator of, the degree of burden control.\\nThis estimate is strikingly low, implying that each observed case in Lagos State represents roughly\\n400 infections. It’s worth considering potential sources of error.\\nThe reporting rate estimate would be biased low if for some reason the model’s transmission\\nprocess is too fast. One plausible issue is age-dependence in surveillance rates – maybe older indi-\\nviduals get measles but systemically are not reported? – and this biases the estimate of expected\\nannual burden. However, the serological survey rules this possibility out. Survey estimates, which\\nclimb to near total immunity by age 5 in Southern Nigeria, suggest that the distribution in Fig. 1is\\nrepresentative, and more generally that large pockets of susceptible individuals cannot go years\\nwithout encountering measles.\\nOther mechanisms that may slow transmission rates like network effects and age-dependence\\nin contact structures are also bounded by the same argument. So while it’s certainly true that\\nthese effects exist in Lagos State, it’s implausible for them to change the order of magnitude of\\nthe reporting rate without unreasonably accumulating susceptibility. Surveillance issues in high-\\nburden settings, like Nigeria and Pakistan (Thakkar et al., 2019 ), seem to warrant further study.\\nModel validation\\nStricter validation tests, on top of the ﬁt to cases and the agreement with the serological survey,\\nhelp us build conﬁdence in the modeling approach and underlying inferences. Forecast accuracy is\\none such test, which tells us if the model’s biological mechanisms and assessments of past catch-up\\nactivities are robust enough to anticipate burden.\\nWe test the Lagos State model in Fig. 3. The approach described in the previous section is\\napplied to the data through the end of 2020 (grey), and the model is extrapolated until 2024 (color).\\nThis 3 year horizon was chosen for it’s operational relevance, since catch-up efforts are generally\\nseparated by 1 to 3 years, and forecasts on that time-scale facilitate implementation.\\nWith that operational context in mind, we assume that the timing of the 2022 vaccination cam-\\npaign is known, as if it’s being planned in early 2021, but the impact on immunity must be extrapo-\\nlated. To do so, we take the average of past vaccination campaign impacts on susceptibility as the\\nexpected effect of the 2022 campaign. Furthermore, we extrapolate the reporting rate to be ﬁxed\\nat the average level estimated by the model at the end of 2020.\\nThe black dots in Fig. 3are the cases actually observed in Lagos State from 2021 to 2024. The\\nmodel-based forecast clearly captures the structure of the 2022 outbreak, it’s timing, and the rate\\n5 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nFigure 3. Forecast testing. Withholding data from 2021 onward (black dots) allows us to test model\\ninferences (grey) and forecasts (orange). Forecasting necessarily involves predicting catch-up vaccination’s\\nimpact on susceptibility (blue).\\nof it’s decline due to the 2022 campaign. The 50 %interval captures 49% of the data and the 95 %\\ninterval captures 89% of the data. In the supplementary information, we show that this type of\\nperformance is typical of the models across the 17 states.\\nIt’s notable that, given cases alone, the rise in 2022 would seem historically unprecedented on\\nthe backdrop of control since 2014. Said differently, we would not expect a purely autoregres-\\nsive model to capture the resurgence. Our approach clearly beneﬁts from mechanistically relating\\ndemographic and vaccination information to outcomes, and this test helps us better understand\\nthose relationships.\\nFor example, the demographic and vaccination information essentially determines the rate of\\nsusceptibility increase during the years of control. In that era, larger catch-up effects would have\\nplaced the model at lower susceptibility entering 2022, and forecasts would have mistimed the\\noutbreak as a result. Meanwhile, if catch-up effects were smaller, events like the 2022 outbreak\\nwould have been observed from 2014 to 2021. In the context of a transmission model, these\\nfeatures of the data constrain unknowns and lead to relatively high-conﬁdence inferences.\\nComparing vaccine delivery modes\\nWith the 17 state-level models in hand, we can return to our scientiﬁc and programmatic goals. We\\nwant to compare the 2019 IRI to the 5 vaccination campaigns.\\nModel-based estimates of per-dose eﬃcacy offer a direct, quantitative measure of intervention\\nimpact that accounts for epidemiological context. More speciﬁcally, associated with each state’s\\nmodel is an approximate, joint posterior distribution over the parameters (Appendix 2), which we\\ncan use to evaluate posterior proﬁles over each catch-up event’s eﬃcacy. This is visualized for\\nLagos in Fig. 4, and the 2019 IRI (green) clearly stands out amongst the 6 catch-up efforts.\\nRelative to the campaigns (blue), there are two key differences. First, the posterior mode is\\nmarkedly higher for the IRI - we estimate that 12.7 %of IRI vaccines immunized a susceptible child,\\ncompared to just over 5.5% averaged across the campaigns. Second, uncertainty for the IRI is\\nhigher, a direct consequence of it’s smaller size. This can also be seen by looking across the cam-\\npaign distributions, and noting that the width is correlated to the number of doses delivered.\\nThese differences are intriguing but should be interpreted carefully. An important limitation of\\nthe proﬁles, which hold all but the eﬃcacy of interest constant, is that they can mask correlation\\n6 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nFigure 4. Catch-up eﬃcacy in Lagos State. Transmission model-based posterior proﬁles can be computed for\\neach catch-up event, essentially quantifying in terms of a probability how quickly the agreement with data\\nchanges as catch-up vaccines become more likely to immunize a child. The 2019 IRI (green) stands out\\namongst the campaigns (blue) even though the model lacks any age structure.\\nFigure 5. Catch-up vaccination across the South. State/catch-up event pairs (dots, 2 standard deviation error\\nbars) across all 17 states maintain the distinction between the IRI (green) and the 5 campaigns (blue), across a\\nwide range of event sizes. (Lower panel) Regional susceptibility (black) helps contextualize the catch-up event\\ntimeline (bars). The 2019 IRI (green) was less than 2 years after the 2018 campaign, in contrast to other,\\nlonger, inter-event times. Meanwhile, the events from 2021 onward were asynchronous across states.\\n7 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nbetween the interventions. For example, Lagos’ 2018 campaign, which leveraged a variety of imple-\\nmentation innovations ( Ihebuzor et al., 2022 ;Bola et al., 2022 ), had exceptionally high surveyed\\ncoverage ( NBS and NPHCDA, 2019). This type of detail isn’t directly incorporated into the model,\\nand if the point estimate for the 2018 campaign is biased low, it’s possible for the 2019 IRI proﬁle\\nto be biased high in a negative correlation that’s not being explored. One potential issue is that\\nthere maybe other context speciﬁc correlations we’re unaware of.\\nWith that in mind, repeating this exercise across Southern states demonstrates that our ﬁnd-\\nings are robust across state-speciﬁc implementation details. This is done in Fig. 5. In the scatter\\nplot, doses delivered are plotted against estimated eﬃcacy (2 standard deviation error bars) for\\nindividual state-catch-up event pairs, with the IRI highlighted in green. Averaged across catch-up\\nevent types (dashed lines), we ﬁnd that 15.6% of IRI vaccines immunized a susceptible child as\\ncompared to 7.3% of campaign vaccines, a 2.13× difference. Posterior distributions mixed over\\nthe South show that uncertainty is higher for the IRI, as we saw in Lagos State alone, but that the\\ndistinction between the delivery modes remains signiﬁcant.\\nOverall, it’s striking that despite the model lacking any explicit age-structure, we can extract\\ninformation relevant to age-targeting through principled inference, particularly by translating age-\\nat-infection information into constraints on unknowns. As an estimation tool, the model presented\\nhere is versatile, parameterized in seconds on a laptop, and has an approachable data footprint.\\nIt offers a general, complementary approach to more conventional survey-based methods of eval-\\nuating interventions.\\nBalancing drivers of eﬃcacy\\nIn retrospect, based on the distribution in Fig. 1, we already expected susceptibility to decline with\\nage, and it’s not so surprising that campaigns targeting younger children are more effective per\\ndose. But the effect in Fig. 5is large. In fact, we see from Fig. 1that the IRI delivered roughly\\nhalf the doses of the average campaign ( ∼2.3M doses), so we might coarsely expect campaigns to\\ndeliver 50 %of their vaccines to children above 2 across the South (see Appendix 1). If all of those\\nolder children were deterministically immune, the ratio of IRI to campaign eﬃcacy would be ∼2,\\nlower than what we’re seeing even in this limiting case.\\nIt seems like there’s more going on. The catch-up activity timeline is contextualized in terms of\\nSouthern Nigeria’s susceptibility (black) in Fig. 5’s lower panel. One other striking feature of the\\n2019 IRI is that it was less than 2 years after the March 2018 campaign. In contrast, inter-campaign\\ntimes since 2011 sometimes exceeded 2 and a half years.\\nFrom an epidemiological stand-point, this few month difference can be signiﬁcant. In particular,\\nin Southern Nigeria, the high-transmission season (Appendix 2) begins in November, and the 2013\\nand 2022 resurgences are characterized by inter-event times that span multiple high transmission\\nseasons, supporting broader evidence ( Rosenfeld et al., 2024 ) that campaign timing is a signiﬁcant\\ndriver of overall impact.\\nIn other words, the 2019 IRI’s beneﬁts were two-fold. As an intervention it was tailored to the\\nhighest susceptibility population, and in being smaller scale, it was possible to implement before\\nthat population was overexposed to measles risk. Delays in the execution of planned campaigns,\\nparticularly in 2013 and 2022, exposed susceptible children to additional high-transmission sea-\\nsons, deﬂating the average campaign eﬃcacy overall. The 2019 IRI is made substantially more\\neffective in comparison.\\nConclusion\\nThe Southern Nigerian epidemiology helps us evaluate different catch-up vaccination approaches.\\nAs it stands, vaccination campaigns and periodic IRIs (PIRIs) are generally thought of as two distinct\\noptions for vaccine delivery, and high-burden settings rely almost exclusively on campaigns to ﬁll\\nimmunity gaps. But the Southern Nigerian experience calls for more ﬂexibility.\\n8 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nThe estimates in this paper point to a reframing of catch-up vaccination decision-making, mov-\\ning away from minimizing event frequency with large target populations towards a system capa-\\nble of maximizing frequency with small target populations. While the former objective was built\\naround the idea that large campaigns interrupt transmission (World Health Organization, 2016;\\nVerguet et al., 2015 ), the latter is more adapted to ﬁlling immunity gaps left open by past efforts.\\nA key ﬁnding along these lines is that even with perfect coverage campaigns, ineligible new-\\nborns in Southern Nigeria quickly age into susceptibility and are left at risk systematically. The cur-\\nrent guidance relies on transmission interruption to protect these kids between catch-up events,\\nbut that seems unrealistic in practice. In fact, looking at susceptibility over time in Southern Nigeria\\n(Fig. 5) and considering the ever-present risk of delays in campaign execution, the minimum fre-\\nquency strategy puts the population in a dangerous situation every 2 years essentially by design.\\nIt’s clear that catch-up vaccination’s global objectives require thoughtful reevaluation.\\nTill then, what we can say concretely is that Southern Nigeria’s 2019 IRI had an effect on measles\\ntransmission comparable to its large campaigns. There are many implementation details needed\\nto fully understand this out-sized impact, but the IRI’s timing and targeting certainly played a sig-\\nniﬁcant role, as both were better adapted to the epidemiology. This historical example suggests\\nthat catch-up vaccination strategies have room for context-speciﬁc improvement as well.\\nLooking ahead, evaluating improvement will require us to complement measures of compre-\\nhensiveness with estimates of epidemiological impact. Transmission models are necessary to do\\nthat, helping us translate the data we observe into the underlying processes we can’t, like the\\nchanges in susceptibility in the wake of a vaccination effort. The principles we’ve used in this study\\nare general in the sense that they can be applied across settings, and they represent a step towards\\nincluding more evidence in intervention design.\\nAcknowledgments\\nThis paper beneﬁted signiﬁcantly from feedback from a large group of colleagues. Speciﬁcally,\\nwe want to thank Katie Maloney, Ana Leticia Nery, Yusuf Yusufari, Shamsudeen Sani, and Bayero\\nUniversity’s group led by Muktar Gadanya for their critical readings and their suggestions for edits.\\nWe also want to thank our colleagues at Nigeria CDC, US CDC, and NPHCDA for their collaboration\\nand data collection expertise, without which this work could not have been done.\\nReferences\\nAnderson RM, May RM. Infectious diseases of humans: dynamics and control. Oxford University Press; 1992.\\nBishop CM. Pattern recognition and machine learning. Springer; 2006.\\nBola O, Oteri AJ, Bawa S, Nkwogu L, Wagai J, Oladele A, Kariya S, Akinbajo A, Yenyi S, Dieng B, et al. The Role of\\nSchool Engagement in Increasing Vaccination During Measles Mass Vaccination Campaign in Nigeria, 2018:\\nThe Lagos State Experience. Journal ISSN. 2022; 2766:2276.\\nDorélien AM . Birth seasonality in sub-Saharan Africa. Demographic Research. 2016; 34:761–796.\\nFerrari MJ, Grais RF, Bharti N, Conlan AJ, Bjørnstad ON, Wolfson LJ, Guerin PJ, Djibo A, Grenfell BT. The dynamics\\nof measles in sub-Saharan Africa. Nature. 2008; 451(7179):679–684.\\nFinkenstädt BF, Grenfell BT. Time series modelling of childhood diseases: a dynamical systems approach.\\nJournal of the Royal Statistical Society Series C: Applied Statistics. 2000; 49(2):187–205.\\nGaythorpe KA, Abbas K, Huber J, Karachaliou A, Thakkar N, Woodruff K, Li X, Echeverria-Londono S, Ferrari M,\\net al. Impact of COVID-19-related disruptions to measles, meningococcal A, and yellow fever vaccination in\\n10 countries. Elife. 2021; 10:e67023.\\nGrais RF, Conlan AJ, Ferrari MJ, Djibo A, Le Menach A, Bjørnstad ON, Grenfell BT. Time is of the essence: explor-\\ning a measles outbreak response vaccination in Niamey, Niger. Journal of the Royal Society Interface. 2008;\\n5(18):67–74.\\n9 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nHalsey NA, Boulos R, Mode F, Andre J, Bowman L, Yaeger RG, Toureau S, Rohde J, Boulos C. Response to\\nmeasles vaccine in Haitian infants 6 to 12 months old: inﬂuence of maternal antibodies, malnutrition, and\\nconcurrent illnesses. New England journal of medicine. 1985; 313(9):544–549.\\nIhebuzor C, Oteri A, Bawa S, Kolawole A, Dieng B, et al. “Campaign Staggering” a Way to Bridge Resources Gaps\\nin Supplemental Immunization Activities-Lagos State 2018 Measles Vaccination Campaign’s Experience. J\\nCommunity Med Public Health. 2022; 6(270):2577–2228.\\nLi X, Goodson JL, Perry RT. Measles Population Immunity Proﬁles: Updated Methods and Tools. Vaccines. 2024;\\n12(8).https://www.mdpi.com/2076-393X/12/8/937, doi: 10.3390/vaccines12080937.\\nMinta AA, Ferrari M, Antoni S, Portnoy A, Sbarra A, Lambert B, Hatcher C, Hsu CH, Ho LL, Steulet C, et al.\\nProgress towards measles elimination–worldwide, 2000-2022/Progres accomplis dans le monde en vue de\\nl’elimination de la rougeole, 2000-2022. Weekly Epidemiological Record. 2023; 98(46):587–599.\\nNakase T, Brownwright T, Okunromade O, Egwuenu A, Ogunbode O, Lawal B, Akanbi K, Grant G, Bassey\\nOO, Coughlin MM, Bankamp B, Adetifa I, Metcalf CJE, Ferrari M. The impact of sub-national hetero-\\ngeneities in demography and epidemiology on the introduction of rubella vaccination programs in Nigeria.\\nVaccine. 2024; 42(20):125982. https://www.sciencedirect.com/science/article/pii/S0264410X24005899 ,doi:\\nhttps://doi.org/10.1016/j.vaccine.2024.05.030.\\nNBS, NPHCDA, 2018 Post Measles Campaign Coverage Survey, Main Report. Abuja, Nigeria: National Bureau of\\nStatistics and National Primary Health Care Development Agency; 2019. https://nphcda.gov.ng/publications/ .\\nNBS, UNICEF, Multiple Indicator Cluster Survey 2011. Abuja, Nigeria: National Bureau of Statistics (NBS) and\\nUnited Nations Children’s Fund (UNICEF); 2012. https://mics.unicef.org/sites/mics/files/Nigeria%202011%\\n20MICS%20Summary_English.pdf .\\nNBS, UNICEF, Multiple Indicator Cluster Survey 2016-17. Abuja, Nigeria: National Bureau of Statistics (NBS) and\\nUnited Nations Children’s Fund (UNICEF); 2017.\\nNBS, UNICEF, Multiple Indicator Cluster Survey 2021. Abuja, Nigeria: National Bureau of Statistics (NBS) and\\nUnited Nations Children’s Fund (UNICEF); 2022. https://mics.unicef.org/sites/mics/files/Nigeria%202021_\\nMICS_SFR_English.pdf .\\nNPC, ICF, Nigeria Demographic and Health Survey 2008. Abuja, Nigeria: National Population Commission - NPC\\nand ICF; 2009. http://dhsprogram.com/pubs/pdf/FR222/FR222.pdf .\\nNPC, ICF, Nigeria Demographic and Health Survey 2013. Abuja, Nigeria: National Population Commission - NPC\\nand ICF; 2014. http://dhsprogram.com/pubs/pdf/FR293/FR293.pdf .\\nNPC, ICF, Nigeria Demographic and Health Survey 2018. Abuja, Nigeria: National Population Commission - NPC\\nand ICF; 2019. http://dhsprogram.com/pubs/pdf/FR359/FR359.pdf .\\nOteri J, Idi Hussaini M, Bawa S, Ibizugbe S, Lambo K, Mogekwu F, Wiwa O, Seaman V, Kolbe-Booysen\\nO, Braka F, Nsubuga P, Shuaib F. Application of the Geographic Information System (GIS) in\\nimmunisation service delivery; its use in the 2017/2018 measles vaccination campaign in Nigeria.\\nVaccine. 2021; 39:C29–C37. https://www.sciencedirect.com/science/article/pii/S0264410X21000256 ,doi:\\nhttps://doi.org/10.1016/j.vaccine.2021.01.021, efforts Towards Measles Elimination in Nigeria: Lesson Learnt\\nand Best Practice of the 2017/2018 Measles Vaccination Campaign.\\nMeasles & Rubella Partnership. Global Measles & Rubella Strategic Framework 2021-2030. . 2020 November;\\nhttps://measlesrubellapartnership.org/learn/the-solution/the-strategy/ .\\nRosenfeld KA , Frey K, McCarthy KA. Optimal Timing Regularly Outperforms Higher Coverage in Preventative\\nMeasles Supplementary Immunization Campaigns. Vaccines. 2024; 12(7). https://www.mdpi.com/2076-393X/\\n12/7/820, doi: 10.3390/vaccines12070820.\\nThakkar N. A modeling approach for estimating dynamic measles case detection rates. arXiv preprint\\narXiv:220211222. 2022; .\\nThakkar N, Abubakar AHA, Shube M, Jama MA, Derow M, Lambach P, Ashmony H, Farid M, Sim SY, O’Connor\\nP, Minta A, Bose AS, Musanhu P, Hasan Q, Bar-Zeev N, Malik SMMR. Estimating the Impact of Vaccination\\nCampaigns on Measles Transmission in Somalia. Vaccines. 2024; 12(3). https://www.mdpi.com/2076-393X/\\n12/3/314, doi: 10.3390/vaccines12030314.\\n10 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nThakkar N , Gilani SSA, Hasan Q, McCarthy KA. Decreasing measles burden by optimizing campaign timing.\\nProceedings of the National Academy of Sciences. 2019; 116(22):11069–11073.\\nVerguet S , Johri M, Morris SK, Gauvreau CL, Jha P, Jit M. Controlling measles using supplemental immunization\\nactivities: a mathematical model to inform optimal policy. Vaccine. 2015; 33(10):1291–1296.\\nWolfson LJ, Grais RF, Luquero FJ, Birmingham ME, Strebel PM. Estimates of measles case fatality ratios: a\\ncomprehensive review of community-based studies. International journal of epidemiology. 2009; 38(1):192–\\n205.\\nWorld Health Organization , Planning and implementing high-quality supplementary immunization activities\\nfor injectable vaccines using an example of measles and rubella vaccines: ﬁeld guide. World Health Organi-\\nzation; 2016.\\nWorld Health Organization. Measles outbreak guide. World Health Organization; 2022. https://iris.who.int/\\nhandle/10665/360891.\\nWorld Health Organization, Immunization Data Portal; 2024. https://immunizationdata.who.int/ , accessed\\nJune 2024.\\n11 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nAppendix 1\\nUntested, isolated, clinically compatible cases\\nAppendix 1—ﬁgure 1. Incidence curves. (Top) We use a logistic regression approach to classify\\nuntested cases (light grey) and compile an incidence curve (red) for every state. (Bottom) Estimated\\nconﬁrmation probability (blue) follows lab activity closely (binomial error bars), exposing fast dynamics\\nin the speciﬁcity of measles’ clinical deﬁnition.\\nSurveillance in Nigeria follows WHO guidelines ( World Health Organization, 2022 ) in the\\nsense that febrile-maculopapular rash cases with cough, coryza, or conjunctivitis are consid-\\nered clinically compatible measles cases. Blood samples taken from those individuals are\\ngenerally tested for anti-measles IgM antibody and are either lab conﬁrmed or rejected. If a\\nclinically compatible case can be linked by investigation to a lab conﬁrmed case, lab testing is\\nskipped and the case is classiﬁed as epidemiologically linked.\\nIn practice, for a variety of reasons, not all clinically compatible cases complete this process.\\nThe situation is visualized for Lagos State in Fig. 1. In the top panel, clinically compatible cases\\nthat were never tested or investigated (light grey) are stacked on top of lab conﬁrmed and\\nlinked cases (dark grey). In 2013, the stark transition from dark to light grey illustrates a typical\\nissue: labs can run out of materials for testing, and lab activity can be temporarily interrupted.\\nIf all untested clinically compatible cases were considered measles, we would erroneously\\nbelieve that transmission rates spontaneously increased in 2013. But that’s clearly not the\\ncase. Lab rejected cases (black dashed line) show that clinically compatible cases are often\\ndue to other viruses, generally more than half the time in Lagos State.\\nAccurate assessment of the quality of interventions requires us to classify these remaining\\nclinically compatible cases. To do so, we assume that the probability of a positive test for a\\nsymptomatic individual, age 𝑎and with𝑑reported vaccine doses, is\\n𝑝(IgM +|𝑎,𝑑,𝑡) =𝜎(𝛽0+𝛽1𝐼𝑑=1+𝛽2𝐼𝑑≥2+𝛽3𝐼𝑑=∅+𝛽4𝐼𝑎>5+𝜀𝑡),\\nwhere𝜎is the logistic function, 𝐼𝑐∈ {0, 1}is an indicator of condition 𝑐, and𝜀𝑡is a random\\nprocess over time 𝑡correlated on the 2 month timescale. Missing dose histories, indicated by\\n𝐼𝑑=∅, are treated as a special case. The parameters 𝛽𝑖for𝑖= 0,.., 4measure relative changes in\\nodds and are estimated jointly with 𝜀𝑡in a logistic regression (Bishop, 2006) over lab conﬁrmed\\nand rejected cases.\\n12 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nThe outcome of this approach for Lagos State is visualized in color in Fig. 1. In red, the ex-\\npected number of measles cases gracefully interpolates the lab interruption in 2013, exposing\\nthe period of measles control from 2014 to 2020 discussed in the main text. While individual-\\nlevel uncertainty is high, aggregated uncertainty (red shading) is barely visible. Meanwhile,\\nin the lower panel, test positivity rates averaged over 𝑎and𝑑(blue) are in keeping with ob-\\nserved conﬁrmation rates (binomial error bars), highlighting measles dynamics on a backdrop\\nof other circulating pathogens. For the models throughout the text, this regression process is\\nrepeated for every state, and measles cases throughout this paper refer to the state’s aggre-\\ngated incidence curve.\\nDoses delivered in catch-up events\\nThe number of vaccines delivered in each catch-up activity is an important model input that\\ncomes with diﬃcult-to-quantify structural uncertainties. We have partial information on catch-\\nup event size at different spatial scales, and in general we’re forced to make some assumptions\\non the size of the different events in each state.\\nFor the campaigns, the WHO collects and publicly releases information on the number of\\ndoses aggregated over all the states participating (World Health Organization, 2024). To build\\nstate-level transmission models, we assume doses are distributed according to the distribu-\\ntion of children born 5 years prior to the campaign.\\nFor the 2019 IRI, the WHO does not report the total number of doses delivered, and we’re\\nforced to incorporate additional implementation data. Speciﬁcally, we rely on ward-level (ad-\\nministrative level 3) aggregates of doses delivered by age in the 2022 campaign in Lagos,\\nGombe, and Ogun states, which we were able to obtain from the Nigerian Primary Health\\nCare Development Agency.\\nThe age distribution of doses delivered across all 726wards (box plots), in comparison to\\nwhat we would expect with a uniform age-distribution (blue), is visualized in Fig. 2. Interest-\\ningly, younger children are over-represented on average, and 2 to 5 year-olds make up 49%\\nof doses delivered, instead of the ∼ 70% we might guess for a 9 month to 5 year target. This\\nseems intuitive, as older children may have already had opportunities for vaccine.\\nAppendix 1—ﬁgure 2. Dose distributions. Ward-level implementation data (box plots) from the 2022\\nvaccination campaign offers insight into the age distribution of children reached by catch-up events,\\nwhich deviates from a binned uniform distribution (blue).\\nTo specify the 2019 IRI in each state, we apply the average distribution in Fig. 2 to each\\nstate’s average 9 month to 5-year campaign size, and we collect the estimated number of\\nvaccines delivered to children under 2. The full table of estimated vaccines delivered by state\\nand event can be found in the repository associated with this paper.\\n13 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nThe above process is imperfect, and ideally we would have visibility into the full set of state-\\nlevel delivery data. That said, in sensitivity testing, results throughout the paper were robust\\nto reasonable changes in this upsampling approach. While the uncertainty in per-dose perfor-\\nmance relies on this information, the uncertainty in relative eﬃcacy is more stable. Moreover,\\nin the transmission model, the key inference is the number of susceptible individuals immu-\\nnized in each event, which is independent of doses delivered as long as that number is large.\\nWe are certainly in that limit since we expect most catch-up doses to be delivered to immune\\nchildren, and we can safely compare catch-up activities as a result.\\nAppendix 2\\nSurvival-based priors\\nModel-based inference follows the process in Thakkar (2022), generalized to incorporate vac-\\ncination and spatial correlation. A fundamental complexity in model-ﬁtting is that, without\\nsome constraints, variation in cases can be explained either by a change in surveillance rates\\n(e.g. catching more cases in a given time-period by chance) or by transmission dynamics (e.g.\\ninfectious people interacted with more susceptible people than usual). In reality, both of these\\neffects happen simultaneously, and we need some mechanism for distinguishing them.\\nIn the literature, this is often done by assuming reporting rates vary smoothly ( Finkenstädt\\nand Grenfell, 2000) or not at all (Ferrari et al., 2008; Thakkar et al., 2019). We take a less\\nrestrictive approach here: We ﬁrst calculate expected burden at an annual timescale, using the\\nage-at-infection distribution to allocate birth-cohorts to infections over time (sometimes called\\na cohort model or an immunity proﬁle ( Li et al., 2024)). Then, in comparing expectations to\\ncases, we can estimate slow variation in the surveillance rate, which we use as an informative\\nprior in the context of a more conventional, fast time-scale transmission model.\\nTo illustrate the basic idea, consider a birth-cohort, size 𝐵𝑏, labelled by birth year 𝑏. If\\nwe assume that individuals in that cohort acquire measles immunity once and then have it\\nfor life, we can try to partition the cohort into groups associated with their immunity source,\\n𝑠∈ {𝐼,𝑉1,𝑉2,𝑉𝑐\\n1,...,𝑉𝑐\\n𝑀}, where𝐼is infection, 𝑉1and𝑉2are the routine vaccines, and 𝑉𝑐\\n𝑖are\\ncatch-up vaccines associated with events 𝑖= 1,...,𝑀 .\\nIn this context, the age distribution of cases gives us insight into 𝑝(𝑎|𝑠 =𝐼,𝑏), the probability\\nof being age 𝑎given that an individual is infected and born in year 𝑏. The object of primary\\ninterest for us, however, is the joint distribution\\n𝑝(𝑎,𝑠 =𝐼|𝑏) =𝑝(𝑠=𝐼|𝑏)𝑝(𝑎|𝑠 =𝐼,𝑏),\\nwhere the prefactor represents the overall fraction of cohort 𝑏destined to be infected. In\\nother words, our goal is to calculate the fraction of each birth cohort left to infection across all\\nages, and then allocate them in time according to the age information associated with cases.\\nSince opportunities for vaccine-derived immunity are ordered, limited, and largely disjoint,\\nwe can make progress case-by-case. In general, each vaccination opportunity happens at a\\nﬁxed time, 𝑡𝑠′, has a known probability of seroconversion, 𝜀(𝑠′), and an associated coverage,\\n𝐶(𝑠′|𝑏). Then,\\n𝑝(𝑠=𝑠′|𝑏) =𝜀(𝑠′)𝐶(𝑠′|𝑏)[\\n1 −𝑝(𝑠=𝐼|𝑏)𝑝(𝑎 ≤𝑡𝑠′−𝑏|𝑠=𝐼,𝑏) −∑\\n𝑠′′<𝑠′𝑝(𝑠=𝑠′′|𝑏)]\\n,\\nwhere in the survival term 𝑠′′<𝑠′refers to the chronological ordering of vaccine opportunities\\nand for𝑠=𝐼we leverage the conditional age distribution. For a given cohort 𝑏, across𝑝(𝑠=\\n𝑠′|𝑏), these recursive equations can be evaluated at various 𝑝(𝑠=𝐼|𝑏). Noting that∑\\n𝑠𝑝(𝑠|𝑏) = 1\\n14 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nis linear in 𝑝(𝑠=𝐼|𝑏), we evaluate the recursion at 2 points and then solve for the 𝑝(𝑠=𝐼|𝑏)\\nthat exactly balances the normalization condition for each cohort.\\nAppendix 2—ﬁgure 1. Survival and immunity. (Left) We leverage a survival analysis across vaccination\\nopportunities (colors) to partition birth cohorts by immunity source. (Right) Infections across cohorts\\ncan be compared to observed cases (blue) to estimate annual-scale variation in reporting rates (purple).\\nIn application to Nigeria, we assume 𝜀(𝑉1) = 0.825,𝜀(𝑉𝑐\\n𝑖) = 0.9, and𝜀(𝑉2) = 0.95 (Halsey\\net al., 1985 ). Coverage of MCV1 is estimated as described in the main text, and for MCV2 it’s\\nscaled for each state by the yearly ratio of national-level MCV2 coverage to MCV1, since we\\nlack MCV2-related survey data. For the catch-up vaccines, we assume 𝐶(𝑉𝑐\\n𝑖|𝑏)is equal to the\\nfraction of cohort 𝑏expected to be within the age-range given the state’s birth-seasonality\\nproﬁle, discounted by an assumed 90%coverage amongst the target-population. Finally, we\\nestimate𝑝(𝑎|𝑠 =𝐼,𝑏) in a catagorical regression against the age information associated with\\ncases assuming the distribution varies smoothly in 𝑎and𝑏and has negligible support for\\n𝑎>25years-old.\\nGiven the distribution 𝑝(𝑎,𝑠 =𝐼|𝑏), expected annual burden is E[𝐼𝑡] =∑\\n𝑏𝐵𝑏𝑝(𝑎=𝑡−𝑏,𝑠=\\n𝐼|𝑏). We can also calculate the expected initial susceptible population (those destined to be in-\\nfected or vaccinated at 𝑡≥2009 from cohorts 𝑏<2009) and associated variance in susceptibility\\nacross outbreaks in a similar way, assuming 𝑝(𝑎|𝑠 =𝐼,𝑏)is static for 𝑏≤2002 and interpolating\\nMCV1 coverage from the introduction year (1978) to the earliest year we have data.\\nApplication of this approach is visualized for Lagos State in Fig. 1. Bars on the left indicate\\nthe fraction of each cohort with immunity across sources as of the start of 2024. Routine ﬁrst-\\ndose coverage (black) is the dominant contribution in Lagos, but catch-up vaccination events\\n(blue shades by event) are critical. Fractions of cohorts missed by vaccination are highlighted\\nin red, exposing periodic increases. The 2 to 3 year frequency of catch-up events leads to\\nchronically missed populations that amount to thousands of infections.\\nRegression against cases (blue) is shown on the right, and while this simplistic model lacks\\nthe interactions needed to produce outbreaks, we capture the observed level of burden. The\\nassociated reporting rate (purple, 1 and 2 standard deviations shaded) rises in recent years,\\nbut with the level remaining low overall. In trying to understand the source of the rise, we\\ncan overlay the trend in rejected cases (black dots), which is indicative of overall surveillance\\n15 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nactivity. The agreement loosely corroborates our inference and suggests that Lagos is in a\\nsituation where increasing testing volume would increase measles reporting rates.\\nBuilding a complete transmission model\\nThe approach in the previous section allows us to constrain the surveillance effects in estab-\\nlished measles transmission models. But with the low reporting rates we’ve uncovered, we\\nalso expect case time series to be sparse, limiting the quality and stability of inferences. With\\nthat in mind, in this section, we describe the model in detail, emphasising the use of spatial\\ncorrelation to stabilize seasonality estimates.\\nWe assume susceptible individuals, 𝑆𝑡, and infectious individuals, 𝐼𝑡, stochastically interact\\nwith one-another in semi-monthly increments 𝑡, approximating the roughly 14 day exposure\\nto rash-onset time associated with measles ( Finkenstädt and Grenfell, 2000; Ferrari et al.,\\n2008). If infectious individuals are immune for life, considering ﬁrst a population in isolation,\\nwe have\\n𝑆𝑡+1=𝑆𝑡+𝐵𝑡−𝑉𝑡−𝐼𝑡+1 (1)\\n𝐼𝑡+1= (𝛾𝛽𝑡𝜀𝑡)𝑆𝑡𝐼𝛼\\n𝑡(2)\\nwhere𝛽𝑡is an annually periodic transmission rate, 𝜀𝑡is a log-normally distributed volatility\\nwith variance V[𝜀𝑡] =𝜎2\\n𝜀, and𝛼≤1accounts for individuals capable of being infected without\\ninfecting others (Finkenstädt and Grenfell, 2000). Susceptibility in the model accumulates\\nwith new births, 𝐵𝑡, and declines with both infections and with immunizing vaccines, 𝑉𝑡.\\nRoutine immunization is the main contributor to 𝑉𝑡, based on coverage estimates, 𝐶𝑡(𝑉1)\\nand𝐶𝑡(𝑉2), applied to 𝐵𝑡with a 9and 15month lag respectively. Otherwise, vaccines come\\nfrom catch-up events 𝑖= 1,...,𝑀 , which contribute terms of the form 𝜇𝑖𝑑𝑖, an unknown per-\\ndose eﬃcacy scaling the assumed known number of doses delivered.\\nTo stabilize inferences, consider a related model for a larger population, which we call the\\nneighborhood of the process deﬁned by Eqs. 1and2. In the neighborhood, we have\\n𝑆𝑁\\n𝑡+1=𝑆𝑁\\n𝑡+𝐵𝑁\\n𝑡−𝑉𝑁\\n𝑡−𝐼𝑁\\n𝑡+1(3)\\n𝐼𝑁\\n𝑡+1= (𝛽𝑡𝜀𝑁\\n𝑡)𝑆𝑁\\n𝑡𝐼𝑁\\n𝑡, (4)\\nwhere neighborhood quantities are marked by an 𝑁superscript and are deﬁned as above.\\nNote speciﬁcally that Eq. 2and Eq. 4share a seasonal transmission rate, 𝛽𝑡, with only a con-\\nstant scale-factor 𝛾in the smaller population. While this relationship doesn’t allow infection\\nor susceptibility to move from the neighborhood to the population of interest, it allows us to\\nmore intentionally inform seasonal variation in transmission under the assumption that the\\ndrivers of seasonality are shared.\\nIn application to Nigeria, we model transmission at the state-level with Eqs. 1and2, and\\nwe consider the state’s geopolitical zone (a collection of 5 or 6 states in Nigeria) to be the\\nneighborhood. In each of the neighborhood’s 𝑍states, we assume,\\n𝐶𝑡∼Binomial(𝐼𝑡,𝑟𝑡)(5)\\nwhere𝐶𝑡are observed cases and 𝑟𝑡is the semi-monthly reporting rate. This allows us to cal-\\nculate statistics like E [𝐼𝑡|𝐶𝑡,𝑟𝑡], E[𝑆0], and V[𝑆0]for every state and then sum to the zone-level\\nas needed.\\n16 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nAppendix 2—ﬁgure 2. Transmission seasonality. Seasonality in Lagos State is estimated during\\nmodel-ﬁtting. The high-transmission season begins in November and continues through February.\\nTo specify the model, we start with the neighborhood process. Consider the dataset 𝐃=\\n{𝐶𝑖\\n𝑡,𝐵𝑖\\n𝑡,𝐶𝑖\\n𝑡(𝑉1),𝐶𝑖\\n𝑡(𝑉2),𝑑𝑖\\n1,...𝑑𝑖\\n𝑀𝑖|𝑖= 1,...𝑍 }over the𝑍relevant states in a given zone. If we as-\\nsume the state of interest corresponds to 𝑖= 1, and we label the annually-aggregated dataset\\ñ𝐃, then the joint inference distribution for the neighborhood parameters is\\n𝑝(𝜇𝑁,𝑆𝑁\\n0,𝑟2\\n𝑡,...,𝑟𝑍\\n𝑡,𝐼𝑁\\n𝑡,𝛽𝑡,𝜀𝑁\\n𝑡|𝐃,̃𝐃)\\n=𝑝(𝜇𝑁|𝐃,̃𝐃)𝑝(𝑆𝑁\\n0,𝑟2\\n𝑡,...,𝑟𝑍\\n𝑡|𝜇𝑁,𝐃,̃𝐃)\\n𝑝(𝐼𝑁\\n𝑡|𝑆𝑁\\n0,𝑟2\\n𝑡,...,𝑟𝑍\\n𝑡,𝜇𝑁,𝐃,̃𝐃)𝑝(𝛽𝑡,𝜀𝑁\\n𝑡|𝜇𝑁,𝑆𝑁\\n0,𝑟2\\n𝑡,...,𝑟𝑍\\n𝑡,𝐼𝑁\\n𝑡,𝐃,̃𝐃)\\n≈𝑝(𝜇𝑁)𝑝(𝑆𝑁\\n0|̃𝐃)𝑝(𝑟2\\n𝑡,...,𝑟𝑍\\n𝑡|̃𝐃)𝑝(𝐼𝑁\\n𝑡|𝐃,𝑟2\\n𝑡,...,𝑟𝑍\\n𝑡)𝑝(𝛽𝑡,𝜀𝑁\\n𝑡|𝐃,𝑆0,𝐼𝑁\\n𝑡,𝜇𝑁),\\nwhere𝜇𝑁is a vector containing every state’s catch-up event eﬃcacies. In the ﬁrst line above\\nwe’ve chosen an exact hierarchical decomposition, and in the second line we’ve made some\\nconditional independence assumptions. From left to right, along the lines of the logic in\\nThakkar (2022), the ﬁrst term is a prior we use to enforce 0≤𝜇𝑁≤1element-wise, the sec-\\nond and third terms correspond to the survival analysis of the previous section, the next term\\nrefers to the observation process in Eq. 5, and the ﬁnal term to the transmission process in\\nEq.4. For the neighborhood, we treat the observation process coarsely, assuming 𝑟𝑖\\n𝑡=E[𝑟𝑖\\n𝑡|̃𝐃]\\ndeterministically which implies 𝐼𝑁\\n𝑡=∑𝑍\\n𝑖=2E[𝐼𝑖\\n𝑡|𝐶𝑖\\n𝑡,𝑟𝑖\\n𝑡]. The ﬁnal term is then a well-speciﬁed\\nlog-linear regression, and we can ﬁnd the joint maximum posterior estimate with standard\\noptimization methods.\\nWith estimates of 𝑆𝑁\\n𝑡and𝐼𝑁\\n𝑡in hand, we can approach Eqns. 1and2in a similar fashion,\\nwith a joint distribution as above but conditional on the neighborhood states as well. This time,\\nhowever, we allow 𝑝(𝑟𝑡|̃𝐃), the survival prior, to have non-zero variance, and we only neglect\\nthe binomial variation in 𝑝(𝐼𝑡|𝐃,𝑟𝑡), so that𝐼𝑡=E[𝐼𝑡|𝐶𝑡,𝑟𝑡]mixed over 𝑝(𝑟𝑡|̃𝐃). The transmission\\nregression now incorporates both models,\\nln𝐼𝑡+1− ln𝑆𝑡= ln𝛾+ ln𝛽𝑡+𝛼ln𝐼𝑡+ ln𝜀𝑡\\nln𝐼𝑁\\n𝑡+1− ln𝑆𝑁\\n𝑡− ln𝐼𝑁\\n𝑡= ln𝛽𝑡+ ln𝜀𝑁\\n𝑡,(6)\\nwith speciﬁed covariates and responses for 𝑡= 1,..,𝑇 . The negative log-posterior is, up to a\\nconstant,\\n\\ue238(𝛾,𝛽𝑡,𝜖𝑡,𝛼,𝑟𝑡,𝑆0,𝜇1,...,𝜇𝑀) = (24𝑇 − 1) ln̂ 𝜎2\\n𝜀+(𝑆0−E[𝑆0])2\\n2V[𝑆0]+∑\\n𝑡(𝑟𝑡−E[𝑟𝑡])2\\n2V[𝑟𝑡], (7)\\nwherê 𝜎𝜀is the maximum-likelihood estimate of 𝜎𝜀associated with the regression in Eq. 6. Eq.\\n7emphasizes the balance between the slow and fast dynamics, with the ﬁrst term capturing\\nfast transmission and the ﬁnal term incorporating the slow variation in reporting. We can\\n17 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\nminimize \\ue238to ﬁnd the best ﬁt model, differentiate it twice to estimate uncertainty, and calcu-\\nlate proﬁles in speciﬁc parameters like 𝜇𝑖. In practice, we approach this optimization problem\\nwith the same algorithm as in Thakkar (2022) (see footnote 3 in that paper), and we sample\\nthe best-ﬁt model to calculate forecasts.\\nIncorporating the neighborhood process gives us more informed seasonality proﬁles with\\nhigher certainty. For completeness, the seasonality proﬁle for Lagos State is visualized in Fig.\\n2. The periodic variation starts to rise in November, as mentioned in the main text, and there\\nis up to ∼ 40% variation from high to low season transmission. As a result, this parameter is\\nboth critical for forecast accuracy and highly relevant for catch-up vaccination implementation.\\nSpatial correlation is therefore a necessary model feature to make estimates at this spatial\\nresolution in a low reporting rate setting.\\n18 of 18 . CC-BY 4.0 International license It is made available under a  is the author/funder, who has granted medRxiv a license to display the preprint in perpetuity. (which was not certified by peer review)The copyright holder for this preprint this version posted February 25, 2025. ; https://doi.org/10.1101/2025.02.24.25322796doi: medRxiv preprint \\n'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["pdf_file = open(\"intensification-paper.pdf\", \"rb\")\n", "\n", "reader = PyPDF2.PdfReader(pdf_file)\n", "text = \"\"\n", "for page in reader.pages:\n", "    text += page.extract_text() + \"\\n\""]}, {"cell_type": "code", "execution_count": null, "id": "307d6cec", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}