# Measles Transmission Model Architecture

This document contains the architectural design of the measles transmission modeling solution with focus on the high level components and how they interact. The system implements mechanistic SIR (Susceptible-Infected-Recovered) transmission models to analyze vaccination campaign effectiveness in Southern Nigeria.

## High-level Component definitions & use

Describes the definitions and use of each component in the design, its technology and the scope of the use of any services.

**System components**

**Core SIR Transmission Model Engine**

The heart of the system implementing neighborhood-regularized SIR models for measles transmission analysis. Built using Python 3.8 with NumPy/SciPy for numerical computations and optimization algorithms.

**Core Functionality: Core SIR Transmission Model Engine**

- **Neighborhood SIR Model**: Implements spatially-regularized SIR transmission dynamics with seasonal transmission patterns
- **Hierarchical Bayesian Estimation**: Fits transmission parameters using maximum likelihood with regularization priors
- **SIA Impact Quantification**: Estimates vaccination campaign effectiveness through susceptible population depletion modeling
- **Multi-scale Modeling**: Supports both neighborhood-level and state-level model fitting with cross-scale regularization

**Architecture Diagram of component: Core SIR Transmission Model Engine**

```mermaid
---
title: SIR Model Engine Architecture
---
flowchart TD
    A["Input Data (_data/)"] --> B["Data Preprocessing"]
    B --> C["NeighborhoodPosterior Class"]
    B --> D["HoodRegularizedModel Class"]
    
    C --> E["Neighborhood Model Fitting"]
    D --> F["State Model Fitting"]
    
    E --> G["Transmission Rate Estimation"]
    F --> G
    
    G --> H["SIA Efficacy Calculation"]
    H --> I["Model Validation"]
    I --> J["Output Serialization (pickle_jar/)"]
    
    K["Prior Information"] --> C
    K --> D
    
    L["Seasonality Constraints"] --> E
    L --> F
```

**Data Processing Pipeline**

Comprehensive data processing system handling demographic surveys, epidemiological surveillance data, and vaccination records. Implements multiple regression with post-stratification for survey analysis.

**Core Functionality: Data Processing Pipeline**

- **Demographic Analysis**: Processes DHS/MICS survey data to estimate birth rates and vaccination coverage using weighted regression
- **Epidemiological Curve Generation**: Applies logistic regression to classify suspected measles cases and estimate true incidence
- **Age Distribution Smoothing**: Uses Bayesian smoothing to estimate age-specific infection patterns by birth cohort
- **Vaccination Calendar Processing**: Processes SIA (Supplementary Immunization Activity) schedules and coverage data

**Architecture Diagram of component: Data Processing Pipeline**

```mermaid
---
title: Data Processing Pipeline Architecture
---
flowchart TD
    A["Raw Survey Data (DHS/MICS)"] --> B["methods/demography/"]
    C["Surveillance Line List"] --> D["methods/epi_curves/"]
    E["Age Distribution Data"] --> F["methods/age_at_inf/"]
    
    B --> G["Weighted Regression Analysis"]
    D --> H["Logistic Classification"]
    F --> I["Bayesian Smoothing"]
    
    G --> J["Birth Rate Estimates"]
    H --> K["Case Probability Estimates"]
    I --> L["Age-specific Infection Patterns"]
    
    J --> M["Processed Data (_data/)"]
    K --> M
    L --> M
    
    N["Population Data"] --> G
    O["Vaccination Records"] --> G
```

**Analysis Orchestration System**

Workflow management system that coordinates model execution across multiple states and generates manuscript figures. Implements subprocess-based parallel execution for computational efficiency.

**Core Functionality: Analysis Orchestration System**

- **Prior Generation**: Orchestrates survival analysis and immunity profiling across all southern Nigerian states
- **Model Execution**: Manages sequential execution of transmission model fitting and validation
- **Figure Generation**: Automates creation of manuscript figures with state-specific parameterization
- **Result Compilation**: Aggregates model outputs into comprehensive summaries and visualizations

**Architecture Diagram of component: Analysis Orchestration System**

```mermaid
---
title: Analysis Orchestration Architecture
---
flowchart TD
    A["GeneratePriors.py"] --> B["SurvivalPrior.py (per state)"]
    B --> C["Prior Compilation"]
    
    C --> D["GenerateStateSummary.py"]
    D --> E["VisualizeInputs.py"]
    D --> F["TransmissionModel.py"]
    D --> G["OutOfSampleTest.py"]
    D --> H["SIAImpactPosteriors.py"]
    
    E --> I["Figure 1: Model Inputs"]
    F --> J["Figure 2: Transmission Results"]
    G --> K["Figure 3: Validation"]
    H --> L["Figure 4: SIA Impact"]
    
    I --> M["PDF Compilation"]
    J --> M
    K --> M
    L --> M
    
    N["State Configuration"] --> B
    N --> E
    N --> F
    N --> G
    N --> H
```

**Visualization and Output System**

Scientific visualization system generating publication-quality figures and statistical summaries. Implements matplotlib-based plotting with PDF compilation for multi-state analysis.

**Core Functionality: Visualization and Output System**

- **Statistical Plotting**: Generates time series plots, posterior distributions, and model fit diagnostics
- **Multi-panel Figures**: Creates complex manuscript figures with multiple subplots and statistical annotations
- **PDF Report Generation**: Compiles state-by-state analysis into comprehensive PDF documents
- **Serialized Output Management**: Manages pickle-based storage of model results for downstream analysis

**Architecture Diagram of component: Visualization and Output System**

```mermaid
---
title: Visualization System Architecture
---
flowchart TD
    A["Model Results (pickle_jar/)"] --> B["Figure Generation Scripts"]
    C["Statistical Summaries"] --> B
    
    B --> D["Matplotlib Plotting"]
    D --> E["Individual Figures (_plots/)"]
    D --> F["PDF Compilation"]
    
    G["State Parameters"] --> B
    H["Color Schemes"] --> D
    I["Layout Templates"] --> D
    
    E --> J["PNG/PDF Outputs"]
    F --> K["Multi-state Reports"]
    
    L["Goodness of Fit Metrics"] --> C
    M["Posterior Samples"] --> C
```

**Data Management and Serialization System**

Persistent storage system managing intermediate results, model outputs, and processed datasets. Implements pickle-based serialization for Python object persistence and CSV-based data exchange.

**Core Functionality: Data Management and Serialization System**

- **Pickle-based Storage**: Serializes complex Python objects including fitted models, posterior distributions, and statistical summaries
- **CSV Data Processing**: Handles structured input data including demographic surveys, epidemiological time series, and vaccination records
- **Intermediate Result Caching**: Stores computationally expensive intermediate results to enable workflow resumption
- **Cross-script Data Exchange**: Facilitates data sharing between analysis scripts through standardized serialization formats

**Architecture Diagram of component: Data Management and Serialization System**

```mermaid
---
title: Data Management Architecture
---
flowchart TD
    A["Input CSV Files (_data/)"] --> B["Data Loading"]
    B --> C["Data Validation"]
    C --> D["Processing Scripts"]

    D --> E["Intermediate Results"]
    E --> F["Pickle Serialization"]
    F --> G["pickle_jar/ Storage"]

    H["Model Objects"] --> F
    I["Statistical Summaries"] --> F
    J["Figure Objects"] --> F

    G --> K["Result Retrieval"]
    K --> L["Downstream Analysis"]

    M["Configuration Files"] --> B
    N["State Parameters"] --> D
```

**Mathematical Model Implementation System**

Numerical computation system implementing Bayesian hierarchical models with spatial regularization. Built on SciPy optimization algorithms and NumPy linear algebra operations.

**Core Functionality: Mathematical Model Implementation System**

- **Bayesian Inference**: Implements maximum likelihood estimation with regularization priors for transmission parameters
- **Spatial Regularization**: Uses neighborhood information to constrain seasonal transmission patterns across geographic regions
- **Uncertainty Quantification**: Generates posterior distributions and confidence intervals for all model parameters
- **Optimization Algorithms**: Employs gradient-based optimization with analytical derivatives for efficient parameter estimation

**Architecture Diagram of component: Mathematical Model Implementation System**

```mermaid
---
title: Mathematical Model Implementation
---
flowchart TD
    A["Prior Distributions"] --> B["Bayesian Model Setup"]
    C["Likelihood Functions"] --> B
    D["Regularization Terms"] --> B

    B --> E["Optimization Problem"]
    E --> F["Gradient Computation"]
    F --> G["SciPy Minimize"]

    G --> H["Parameter Estimates"]
    H --> I["Uncertainty Quantification"]
    I --> J["Posterior Sampling"]

    K["Spatial Constraints"] --> E
    L["Seasonal Priors"] --> E

    J --> M["Model Validation"]
    M --> N["Goodness of Fit"]
```
