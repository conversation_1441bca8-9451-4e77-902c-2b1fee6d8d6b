# Measles Transmission Model Use Cases

This document describes the specific use cases implemented in the measles transmission modeling system for analyzing vaccination campaign effectiveness in Southern Nigeria.

**Project directories**
- / (root): Main analysis scripts and configuration
- methods/: Core modeling libraries and data processing workflows
- _data/: Processed input data (surveillance, demographics, vaccination)
- _plots/: Generated figures and visualizations
- pickle_jar/: Serialized model outputs and intermediate results

The project implements mechanistic SIR transmission models to compare Southern Nigeria's 2019 routine immunization intensification to mass vaccination campaigns since 2010, finding that age-targeted efforts were more than twice as effective per dose.

## USE-CASE: Prior Generation and Model Initialization

**Feature 1: State-level Immunity Profile Generation**

|| definition |
|--|--|
| GIVEN | Demographic survey data, vaccination records, and epidemiological surveillance data for southern Nigerian states |
| WHEN | GeneratePriors.py is executed to initialize the modeling system |
| THEN | Survival analysis generates immunity profiles and initial susceptible population estimates for each state with uncertainty quantification |

**State Diagram: Logic flow within feature**

This diagram shows the sequential process of generating priors for transmission model initialization across multiple states.

```mermaid
---
title: Prior Generation Workflow
---
stateDiagram-v2
    [*] --> LoadStateList
    LoadStateList --> InitializePriorGeneration
    InitializePriorGeneration --> ProcessState
    ProcessState --> RunSurvivalAnalysis
    RunSurvivalAnalysis --> EstimateImmunityProfile
    EstimateImmunityProfile --> CalculateInitialSusceptible
    CalculateInitialSusceptible --> SerializeResults
    SerializeResults --> CheckMoreStates
    CheckMoreStates --> ProcessState : More states
    CheckMoreStates --> CompilePriors : All states complete
    CompilePriors --> GeneratePriorSummary
    GeneratePriorSummary --> [*]
```

**Sequence Diagram: Interactions between systems to enable Feature**

This flowchart shows how the prior generation system coordinates subprocess execution and data compilation.

```mermaid
---
title: Prior Generation System Interactions
---
flowchart TD
    A["GeneratePriors.py"] --> B["Load State Configuration"]
    B --> C["For Each State"]
    C --> D["Launch SurvivalPrior.py Subprocess"]
    D --> E["Process Demographic Data"]
    E --> F["Fit Immunity Model"]
    F --> G["Generate Survival Curves"]
    G --> H["Serialize State Results"]
    H --> I["Store in pickle_jar/"]
    I --> J["Compile Figure"]
    J --> K["Add to PDF Book"]
    K --> L["Next State"]
    L --> C
    L --> M["Aggregate All Results"]
    M --> N["Generate Final Prior Dataset"]
```

**Data Entity Relationship: Data structure for entities in Feature**

This diagram shows the data relationships in the prior generation system.

```mermaid
---
title: Prior Generation Data Model
---
erDiagram
    STATE ||--|| IMMUNITY_PROFILE : generates
    STATE ||--|| DEMOGRAPHIC_DATA : uses
    STATE ||--|| VACCINATION_HISTORY : analyzes
    
    IMMUNITY_PROFILE {
        string state_name
        float initial_susceptible
        float susceptible_variance
        array age_distribution
        datetime time_index
    }
    
    DEMOGRAPHIC_DATA {
        string state_name
        float birth_rate
        float birth_variance
        array population_by_age
        datetime survey_date
    }
    
    VACCINATION_HISTORY {
        string state_name
        array mcv1_coverage
        array mcv2_coverage
        array sia_dates
        array sia_coverage
    }
    
    PRIOR_COMPILATION ||--o{ IMMUNITY_PROFILE : aggregates
    PRIOR_COMPILATION {
        string compilation_id
        datetime creation_date
        array state_list
        string output_path
    }
```

## USE-CASE: Transmission Model Fitting and Analysis

**Feature 1: SIR Model Parameter Estimation**

|| definition |
|--|--|
| GIVEN | Prior estimates, epidemiological time series, and SIA intervention data for a specific state |
| WHEN | TransmissionModel.py is executed with state-specific parameters |
| THEN | Neighborhood-regularized SIR model estimates transmission rates, seasonal patterns, and SIA efficacy with goodness-of-fit validation |

**State Diagram: Logic flow within feature**

This diagram shows the transmission model fitting process with neighborhood regularization.

```mermaid
---
title: Transmission Model Fitting Process
---
stateDiagram-v2
    [*] --> LoadModelInputs
    LoadModelInputs --> PrepareNeighborhoodData
    PrepareNeighborhoodData --> FitNeighborhoodModel
    FitNeighborhoodModel --> ExtractSeasonalityPrior
    ExtractSeasonalityPrior --> InitializeStateModel
    InitializeStateModel --> FitRegularizedModel
    FitRegularizedModel --> EstimateTransmissionRates
    EstimateTransmissionRates --> CalculateSIAEfficacy
    CalculateSIAEfficacy --> ValidateModelFit
    ValidateModelFit --> GenerateTrajectories
    GenerateTrajectories --> SerializeResults
    SerializeResults --> [*]
    
    ValidateModelFit --> AdjustParameters : Poor fit
    AdjustParameters --> FitRegularizedModel
```

**Sequence Diagram: Interactions between systems to enable Feature**

This flowchart shows the hierarchical model fitting process from neighborhood to state level.

```mermaid
---
title: Transmission Model System Flow
---
flowchart TD
    A["TransmissionModel.py"] --> B["Load State Configuration"]
    B --> C["Prepare Model Inputs"]
    C --> D["Load Neighborhood Data"]
    D --> E["Fit Neighborhood SIR Model"]
    E --> F["Extract Seasonality Prior"]
    F --> G["Initialize State Model"]
    G --> H["Fit Regularized State Model"]
    H --> I["Estimate SIA Efficacy"]
    I --> J["Generate Model Trajectories"]
    J --> K["Calculate Goodness of Fit"]
    K --> L["Validate Against Observations"]
    L --> M["Serialize Model Results"]
    M --> N["Generate Visualization"]
```

## USE-CASE: Out-of-Sample Model Validation

**Feature 1: Predictive Performance Assessment**

|| definition |
|--|--|
| GIVEN | Fitted transmission model and held-out surveillance data for validation period |
| WHEN | OutOfSampleTest.py is executed to assess model predictive performance |
| THEN | Model generates predictions for validation period with uncertainty bounds and calculates performance metrics (R², RMSE) |

**State Diagram: Logic flow within feature**

This diagram shows the validation process using temporal data splitting.

```mermaid
---
title: Out-of-Sample Validation Process
---
stateDiagram-v2
    [*] --> LoadFittedModel
    LoadFittedModel --> DefineValidationPeriod
    DefineValidationPeriod --> SplitTrainingData
    SplitTrainingData --> RefitModelOnTraining
    RefitModelOnTraining --> GeneratePredictions
    GeneratePredictions --> CalculateUncertainty
    CalculateUncertainty --> CompareToObservations
    CompareToObservations --> CalculateMetrics
    CalculateMetrics --> AssessPerformance
    AssessPerformance --> GenerateValidationPlot
    GenerateValidationPlot --> [*]
```

## USE-CASE: SIA Impact Analysis and Comparison

**Feature 1: Vaccination Campaign Effectiveness Quantification**

|| definition |
|--|--|
| GIVEN | Fitted transmission models and historical SIA intervention data across multiple campaigns |
| WHEN | SIAImpactAnalysis.py is executed to compare campaign effectiveness |
| THEN | System quantifies doses required per infection prevented for each campaign type and generates comparative effectiveness estimates |

**State Diagram: Logic flow within feature**

This diagram shows the SIA impact analysis workflow comparing different vaccination strategies.

```mermaid
---
title: SIA Impact Analysis Process
---
stateDiagram-v2
    [*] --> LoadSIAData
    LoadSIAData --> IdentifyCampaignTypes
    IdentifyCampaignTypes --> ProcessMassCampaigns
    ProcessMassCampaigns --> ProcessTargetedCampaigns
    ProcessTargetedCampaigns --> EstimateCounterfactual
    EstimateCounterfactual --> CalculateInfectionsPrevented
    CalculateInfectionsPrevented --> ComputeDosesPerInfectionPrevented
    ComputeDosesPerInfectionPrevented --> CompareEffectiveness
    CompareEffectiveness --> GenerateComparativeAnalysis
    GenerateComparativeAnalysis --> [*]
```

**Data Entity Relationship: Data structure for entities in Feature**

This diagram shows the data relationships in SIA impact analysis.

```mermaid
---
title: SIA Impact Analysis Data Model
---
erDiagram
    SIA_CAMPAIGN ||--o{ VACCINATION_EVENT : contains
    SIA_CAMPAIGN ||--|| EFFECTIVENESS_ESTIMATE : generates
    
    SIA_CAMPAIGN {
        string campaign_id
        string campaign_type
        datetime start_date
        datetime end_date
        string target_population
        float total_doses
    }
    
    VACCINATION_EVENT {
        string event_id
        string campaign_id
        datetime event_date
        float doses_administered
        string age_group
        string geographic_area
    }
    
    EFFECTIVENESS_ESTIMATE {
        string campaign_id
        float infections_prevented
        float doses_per_infection_prevented
        float confidence_interval_lower
        float confidence_interval_upper
        string comparison_baseline
    }
    
    COUNTERFACTUAL_SCENARIO ||--|| EFFECTIVENESS_ESTIMATE : informs
    COUNTERFACTUAL_SCENARIO {
        string scenario_id
        string description
        array susceptible_trajectory
        array infection_trajectory
    }
```

## USE-CASE: Manuscript Figure Generation and Visualization

**Feature 1: Automated Scientific Visualization**

|| definition |
|--|--|
| GIVEN | Model results, statistical summaries, and state-specific parameters |
| WHEN | Individual figure generation scripts (VisualizeInputs.py, etc.) are executed |
| THEN | Publication-quality figures are generated with proper statistical annotations, uncertainty bounds, and comparative visualizations |

**State Diagram: Logic flow within feature**

This diagram shows the figure generation process for manuscript publication.

```mermaid
---
title: Figure Generation Workflow
---
stateDiagram-v2
    [*] --> LoadModelResults
    LoadModelResults --> SelectVisualizationType
    SelectVisualizationType --> PrepareDataForPlotting
    PrepareDataForPlotting --> ApplyPlotTemplates
    ApplyPlotTemplates --> GenerateStatisticalAnnotations
    GenerateStatisticalAnnotations --> AddUncertaintyBounds
    AddUncertaintyBounds --> FormatForPublication
    FormatForPublication --> SaveFigure
    SaveFigure --> [*]

    SelectVisualizationType --> ModelInputs : Figure 1
    SelectVisualizationType --> TransmissionResults : Figure 2
    SelectVisualizationType --> ValidationPlots : Figure 3
    SelectVisualizationType --> SIAImpactPlots : Figure 4
```

## USE-CASE: Multi-State Analysis and Compilation

**Feature 1: Comprehensive Regional Analysis**

|| definition |
|--|--|
| GIVEN | Individual state model results and regional grouping configuration |
| WHEN | GenerateStateSummary.py orchestrates analysis across all southern Nigerian states |
| THEN | System generates comprehensive PDF reports with state-by-state comparisons and regional summaries |

**Sequence Diagram: Interactions between systems to enable Feature**

This flowchart shows the multi-state analysis orchestration process.

```mermaid
---
title: Multi-State Analysis Orchestration
---
flowchart TD
    A["GenerateStateSummary.py"] --> B["Load State List"]
    B --> C["Initialize PDF Book"]
    C --> D["For Each State"]
    D --> E["Launch VisualizeInputs.py"]
    E --> F["Launch TransmissionModel.py"]
    F --> G["Launch OutOfSampleTest.py"]
    G --> H["Launch SIAImpactPosteriors.py"]
    H --> I["Collect Serialized Results"]
    I --> J["Compile State Figure"]
    J --> K["Add to PDF Book"]
    K --> L["Next State"]
    L --> D
    L --> M["Aggregate SIA Distributions"]
    M --> N["Generate Regional Summary"]
    N --> O["Finalize PDF Report"]
```
