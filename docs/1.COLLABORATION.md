# COLLABORATION GUIDE: Measles Transmission Model Project

## Overview

This project uses state-level measles transmission models to compare Southern Nigeria's 2019 intensification of routine immunization to its mass vaccination campaigns since 2010. The repository contains Python 3.8 code associated with the manuscript "Routine immunization intensification, vaccination campaigns, and measles transmission in Southern Nigeria" (2025). The project fits mechanistic transmission models to surveillance and survey data to estimate catch-up vaccination events' effects on measles immunity, finding that the age-targeted 2019 effort was more than twice as effective per dose as the mass campaigns.

---

## Project Structure & Responsibilities

```
/ (root)
├── environment.yml - Conda environment configuration with Python 3.8 and scientific computing dependencies
├── README.md - Project overview and execution instructions
├── LICENSE - Project license file
│
├── GeneratePriors.py - Main script to generate priors for all states (run first, ~20 min)
├── GenerateStateSummary.py - Main script to generate state summaries (run second)
├── VisualizeInputs.py - Creates Figure 1 (model inputs visualization)
├── TransmissionModel.py - Creates Figure 2 (transmission model results)
├── OutOfSampleTest.py - Creates Figure 3 (out-of-sample validation)
├── SIAImpactPosteriors.py - Creates Figure 4 (SIA impact posteriors)
├── SIAImpactAnalysis.py - Creates Figure 5 (SIA impact analysis)
├── SurvivalPrior.py - Creates Appendix 2, Figure 1 (survival prior)
├── SIACalendar.py - SIA calendar processing script
├── SIAImpactAnalysis.py - SIA impact analysis utilities
│
├── _data/ - Processed model inputs and data files
│   ├── binned_age_distributions.csv
│   ├── birth_seasonality_profiles.csv
│   ├── grid3_population_by_state.csv
│   ├── imputed_sia_calendar_by_state.csv
│   ├── monthly_births_by_state.csv
│   ├── southern_age_at_infection.csv
│   ├── southern_states_epi_timeseries.csv
│   ├── states_and_regions.csv
│   └── survey_mcv1_summary_stats.csv
│
├── _plots/ - Generated figures and visualizations
│   ├── model_inputs.png, model_overview.png, model_seasonality.png
│   ├── out_of_sample_test.png, prior_summary.pdf
│   ├── sia_conditional_dists.png, state_models_summary.pdf
│   └── survival_prior.png
│
├── methods/ - Core library functions and classes
│   ├── __init__.py - Package initialization
│   ├── neighborhood_sir.py - Main SIR model implementation (used directly in manuscript)
│   ├── age_at_inf/ - Age distribution of infections processing
│   ├── demography/ - Survey data processing and demographic modeling
│   └── epi_curves/ - Epidemiological curve analysis and logistic regression
│
├── pdfs/ - Research papers and documentation
│   └── intensification-paper.pdf
│
├── pickle_jar/ - Serialized model outputs and intermediate results
│
└── docs/ - Project documentation (including this file)
```

---

## Key Technologies

- **Python 3.8**: Core programming language for all analysis and modeling
- **NumPy 1.18.5**: Numerical computing and array operations for model calculations
- **SciPy 1.5.0**: Scientific computing library for optimization and statistical functions
- **Matplotlib 3.2.2**: Plotting and visualization for generating manuscript figures
- **Pandas 1.2.4**: Data manipulation and analysis for processing surveillance and survey data
- **Scikit-learn 0.23.1**: Machine learning library for regression and statistical modeling

---

## Project Details: File & Directory Responsibilities

### Core Analysis Scripts

**Main Execution Scripts**
- `GeneratePriors.py`: Orchestrates prior generation for all southern states by calling SurvivalPrior.py as subprocess
- `GenerateStateSummary.py`: Compiles state-level summaries and model outputs
- Individual figure generation scripts: Each creates specific manuscript figures with Lagos as default state

### Data Processing (`methods/`)

**Core Model Implementation**
- `neighborhood_sir.py`: Main SIR (Susceptible-Infected-Recovered) transmission model implementation

**Data Processing Workflows**
- `epi_curves/`: Logistic regression for estimating measles case probabilities from clinical data
- `age_at_inf/`: Age distribution smoothing for infection patterns by birth cohort  
- `demography/`: Multiple regression with post-stratification for survey data analysis

### Data & Outputs

**Input Data (`_data/`)**
- Processed model inputs including population, birth rates, vaccination coverage, and epidemiological time series
- No raw surveillance or survey data included (processed outputs only)

**Generated Outputs (`_plots/`, `pickle_jar/`)**
- Manuscript figures and supplementary visualizations
- Serialized model results for reuse across scripts

## Tools

### Tool: Conda

**Requirements**
- Conda package manager installed
- Python 3.8 environment support

**Configurations Conda**
- `environment.yml`: Defines the 'tsir' environment with Python 3.8 and scientific computing dependencies

---

## Setup & Development

### Prerequisites

- Conda package manager
- Python 3.8 support
- Approximately 20 minutes for initial model generation

### Install Dependencies

```sh
# Create and activate the conda environment
conda env create -f environment.yml
conda activate tsir
```

## Build & Deployment

### Build

**Initial Setup (Required)**
```sh
# Must be run in this order (takes ~20 minutes total)
python GeneratePriors.py
python GenerateStateSummary.py
```

**Figure Generation**
```sh
# Generate manuscript figures (default: Lagos State)
python VisualizeInputs.py      # Figure 1
python TransmissionModel.py    # Figure 2  
python OutOfSampleTest.py      # Figure 3
python SIAImpactPosteriors.py  # Figure 4
python SIAImpactAnalysis.py    # Figure 5
python SurvivalPrior.py        # Appendix 2, Figure 1

# Generate figures for specific states
python TransmissionModel.py oyo
python VisualizeInputs.py kano
```

### Deploy

This is a research analysis project - no deployment required. Results are generated as figures in `_plots/` directory and serialized outputs in `pickle_jar/`.

## Debugging

**Debugging Strategy: Build error recovery**

```sh
# If scripts fail, check environment first
conda activate tsir
python --version  # Should be 3.8.x

# Verify data files exist
ls _data/
ls methods/

# Check for missing dependencies
conda list
```

**DEBUGGING: GOTCHAs AND RECOVERY**

- The ERROR: "Unexpected eof" (end of file) | Can typically mean there is a missing or extra bracket/parenthesis. However, can be some times caused by mis-aligned quote types (i.e. opening quote: \' while closing quote '") or additionally by missing tags. Please rewrite the given file to fix the error even if it looks correct. Mistakes happen.
- **Missing pickle files**: If scripts fail with missing serialized data, ensure GeneratePriors.py and GenerateStateSummary.py have completed successfully
- **State name errors**: Use exact state names from `_data/states_and_regions.csv` when running scripts with state arguments
- **Memory issues**: The 20-minute initial setup processes all southern states - ensure sufficient RAM available

## Git Commit Best 

The require git commit policies to follow.

**Git Commands**

- Use the `git status` command to get a clear view of what you are updating.
- Add and commit your changes with a helpful message using `git add -A && git commit -m '[HELPFUL COMMIT MESSAGE HERE]'`

**Basic Rules**
- Git commits should be a wrapper for related changes. For example, fixing two different bugs should produce two separate commits. 
- Commit Often to keep your commits small to enable better reporting on changes and git history management.
- Don't Commit Half-Done Work, only commit code when a logical component is completed. Split a feature's implementation into logical chunks that can be completed quickly so that you can commit often.
- Test Your Code Before You Commit. Follow the Debugging Strategies.
Resist the temptation to commit something that you «think» is completed. Test it thoroughly by making sure the code builds.
- Write clear and Good Commit Messages and keep [.docs/CHANGELOG.md] is up to date. Begin your message with a short summary of your changes (up to 50 characters as a guideline). Separate it from the following body by including a blank line. The body of your message should provide detailed answers to the following questions: – What was the motivation for the change? – How does it differ from the previous implementation? Use the imperative, present tense («change», not «changed» or «changes») to be consistent with generated messages from commands like git merge.


## Collaboration Tips

A summarization of the Collaboration tips and tricks.

- Keep data processing (`methods/`) and analysis scripts (root level) separated for clarity.
- Use clear commit messages and PR descriptions.
- Document new components, APIs, and scripts in the `docs/` folder.
- Update this guide as the project evolves.
- Remember this is research code - prioritize reproducibility and documentation over optimization.
- Always run GeneratePriors.py and GenerateStateSummary.py before other analysis scripts.

## 🔬📚 Research

Find all the research docs related to this project in the directory [./.docs/research/].

**‼️ Rules**

- ✅ Always provide link or path reference to resources used from this Research. Use Oxford Academic citing style, both inline and as a footnote.
- ✅ Always prefer research notes and documentation provided here over your own knowledge.

**📝 Notes**

A set of notes, as markdown files of research relating to topics relavent to this project can be found in [./.docs/research/note-*.md]. Reference these to inform implementations related this this project.

**🌐 Confluence Page documents**

Contains a list of relevant [./.docs/research/confluence_pages.md] researched in this project following the template [./.docs/ai/templates/__confluence_pages.md].

**🌐 Web link documents**

Contains a list of relevant [./.docs/research/web_links.md] researched in this project following the template [./.docs/ai/templates/__web_links.md].
